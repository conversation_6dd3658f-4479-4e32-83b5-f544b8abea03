/* Reset default styles */
body, h1, h2, h3, p, ul, li, button, input, textarea {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Enhanced full-screen black background with vibrant effects */
body {
    font-family: 'Inter', 'Segoe UI', Arial, sans-serif;
    min-height: 100vh;
    width: 100vw;
    margin: 0;
    padding: 0;
    background: 
        radial-gradient(circle at 20% 80%, rgba(255, 20, 147, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(0, 255, 255, 0.12) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(255, 215, 0, 0.08) 0%, transparent 50%),
        radial-gradient(circle at 60% 60%, rgba(138, 43, 226, 0.1) 0%, transparent 50%),
        linear-gradient(135deg, #000000 0%, #0a0a0a 15%, #1a1a1a 30%, #0f0f0f 45%, #000000 60%, #0a0a0a 75%, #1a1a1a 90%, #000000 100%);
    background-size: 500% 500%, 400% 400%, 300% 300%, 350% 350%, 400% 400%;
    animation: 
        bgMove 25s ease-in-out infinite alternate,
        bgPulse 12s ease-in-out infinite;
    color: #ffffff;
    position: relative;
    overflow-x: hidden;
}

/* Enhanced floating particles with more variety */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        radial-gradient(2px 2px at 20px 30px, rgba(255, 20, 147, 0.8), transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(0, 255, 255, 0.7), transparent),
        radial-gradient(1px 1px at 90px 40px, rgba(255, 215, 0, 0.9), transparent),
        radial-gradient(1px 1px at 130px 80px, rgba(138, 43, 226, 0.8), transparent),
        radial-gradient(1px 1px at 180px 120px, rgba(255, 69, 0, 0.7), transparent),
        radial-gradient(1px 1px at 220px 80px, rgba(0, 255, 127, 0.8), transparent),
        radial-gradient(1.5px 1.5px at 280px 150px, rgba(255, 105, 180, 0.6), transparent),
        radial-gradient(1px 1px at 320px 60px, rgba(255, 0, 255, 0.7), transparent);
    background-repeat: repeat;
    background-size: 400px 400px;
    animation: float 25s linear infinite;
    pointer-events: none;
    z-index: 1;
}

/* Add a second layer of particles for depth */
body::after {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        radial-gradient(1px 1px at 50px 100px, rgba(255, 255, 255, 0.4), transparent),
        radial-gradient(1px 1px at 150px 200px, rgba(255, 255, 255, 0.3), transparent),
        radial-gradient(1px 1px at 250px 50px, rgba(255, 255, 255, 0.5), transparent),
        radial-gradient(1px 1px at 350px 180px, rgba(255, 255, 255, 0.4), transparent);
    background-repeat: repeat;
    background-size: 300px 300px;
    animation: float 20s linear infinite reverse;
    pointer-events: none;
    z-index: 1;
}

@keyframes bgMove {
    0% { background-position: 0% 50%, 0% 0%, 0% 0%, 0% 0%; }
    100% { background-position: 100% 50%, 100% 100%, 100% 100%, 100% 100%; }
}

@keyframes bgPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
}

@keyframes float {
    0% { transform: translateY(0px) rotate(0deg); }
    100% { transform: translateY(-200px) rotate(360deg); }
}

/* Enhanced glassmorphism panels with neon borders */
.panel, .card, .container {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid transparent;
    border-radius: 24px;
    background-clip: padding-box;
    box-shadow: 
        0 8px 32px rgba(31, 38, 135, 0.37),
        inset 0 1px 0 rgba(255, 255, 255, 0.2),
        0 0 20px rgba(255, 20, 147, 0.1);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    padding: 32px;
    margin-bottom: 32px;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    z-index: 2;
}

/* Add neon border effect */
.panel::before, .card::before, .container::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #ff1493, #8a2be2, #00ffff, #ffd700, #ff1493);
    border-radius: 26px;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.4s ease;
}

.panel:hover::before, .card:hover::before, .container:hover::before {
    opacity: 0.6;
}

/* Main container with full-page layout */
.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 40px;
    min-height: 100vh;
}

/* Enhanced interactive panel hover effects with 3D transforms */
.panel:hover, .card:hover {
    transform: translateY(-8px) scale(1.02) rotateX(2deg);
    box-shadow: 
        0 20px 60px rgba(31, 38, 135, 0.5),
        inset 0 1px 0 rgba(255, 255, 255, 0.3),
        0 0 30px rgba(255, 20, 147, 0.4),
        0 0 60px rgba(138, 43, 226, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
    background: rgba(255, 255, 255, 0.15);
}

/* Header enhanced with neon glow and 3D effects */
header.app-header {
    background: linear-gradient(135deg, 
        rgba(255, 20, 147, 0.9) 0%, 
        rgba(138, 43, 226, 0.9) 50%, 
        rgba(0, 255, 255, 0.9) 100%);
    color: #fff;
    border-radius: 28px;
    box-shadow: 
        0 8px 40px rgba(255, 20, 147, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.3),
        0 0 30px rgba(255, 20, 147, 0.3),
        0 0 60px rgba(138, 43, 226, 0.2);
    margin-bottom: 40px;
    text-align: center;
    position: relative;
    overflow: hidden;
    border: 2px solid rgba(255, 255, 255, 0.2);
    padding: 40px;
    transform-style: preserve-3d;
    perspective: 1000px;
}

header.app-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 3s infinite;
}

header.app-header::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
    animation: headerGlow 4s ease-in-out infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

@keyframes headerGlow {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 0.8; }
}

/* Enhanced logo with 3D float and glow */
.logo svg {
    filter: drop-shadow(0 0 20px rgba(255, 255, 255, 0.8));
    margin-bottom: 12px;
    animation: logoFloat 3s ease-in-out infinite;
    transform-style: preserve-3d;
}

@keyframes logoFloat {
    0%, 100% { transform: translateY(0px) rotateY(0deg); }
    50% { transform: translateY(-8px) rotateY(10deg); }
}

/* Enhanced title with neon text effect */
.title {
    font-size: 2.5rem;
    font-weight: 800;
    letter-spacing: 1.5px;
    margin-top: 12px;
    background: linear-gradient(45deg, #fff, #f0f8ff, #fff);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: titleGlow 4s ease-in-out infinite;
    text-shadow: 
        0 4px 20px rgba(255, 255, 255, 0.3),
        0 0 30px rgba(255, 20, 147, 0.5),
        0 0 60px rgba(138, 43, 226, 0.3);
    position: relative;
}

.title::after {
    content: attr(data-text);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, #ff1493, #8a2be2, #00ffff);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: titleGlow 4s ease-in-out infinite reverse;
    opacity: 0.7;
    z-index: -1;
}

@keyframes titleGlow {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

/* Enhanced buttons with neon glow and 3D effects */
.btn, button {
    background: linear-gradient(135deg, #ff1493 0%, #8a2be2 100%);
    color: #fff;
    border: none;
    padding: 18px 40px;
    border-radius: 20px;
    font-size: 1.2rem;
    font-weight: 600;
    cursor: pointer;
    box-shadow: 
        0 6px 25px rgba(255, 20, 147, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.3),
        0 0 20px rgba(255, 20, 147, 0.3),
        0 0 40px rgba(138, 43, 226, 0.2);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    outline: none;
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.2);
    text-transform: uppercase;
    letter-spacing: 1px;
    transform-style: preserve-3d;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn:active, button:active {
    transform: scale(0.96) translateZ(-10px);
}

.btn:hover, button:hover {
    background: linear-gradient(135deg, #8a2be2 0%, #ff1493 100%);
    box-shadow: 
        0 12px 40px rgba(138, 43, 226, 0.6),
        inset 0 1px 0 rgba(255, 255, 255, 0.4),
        0 0 30px rgba(255, 20, 147, 0.5),
        0 0 60px rgba(138, 43, 226, 0.4);
    transform: translateY(-5px) scale(1.08) translateZ(10px);
    border-color: rgba(255, 255, 255, 0.4);
}

/* Special button styles with enhanced neon effects */
.btn-primary {
    background: linear-gradient(135deg, #ff1493 0%, #ff69b4 100%);
    box-shadow: 
        0 6px 25px rgba(255, 20, 147, 0.5),
        0 0 30px rgba(255, 20, 147, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #ff69b4 0%, #ff1493 100%);
    box-shadow: 
        0 12px 40px rgba(255, 105, 180, 0.7),
        0 0 50px rgba(255, 20, 147, 0.5);
}

.btn-secondary {
    background: linear-gradient(135deg, #00ffff 0%, #0080ff 100%);
    box-shadow: 
        0 6px 25px rgba(0, 255, 255, 0.5),
        0 0 30px rgba(0, 255, 255, 0.3);
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #0080ff 0%, #00ffff 100%);
    box-shadow: 
        0 12px 40px rgba(0, 128, 255, 0.7),
        0 0 50px rgba(0, 255, 255, 0.5);
}

.btn-mic {
    background: linear-gradient(135deg, #ffd700 0%, #ffa500 100%);
    box-shadow: 
        0 6px 25px rgba(255, 215, 0, 0.5),
        0 0 30px rgba(255, 215, 0, 0.3);
}

.btn-mic:hover {
    background: linear-gradient(135deg, #ffa500 0%, #ffd700 100%);
    box-shadow: 
        0 12px 40px rgba(255, 165, 0, 0.7),
        0 0 50px rgba(255, 215, 0, 0.5);
}

/* Enhanced drop zone with animated rainbow border */
.drop-zone {
    border: 4px dashed transparent;
    border-radius: 24px;
    background: 
        linear-gradient(rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.15)) padding-box,
        linear-gradient(45deg, #ff1493, #8a2be2, #00ffff, #ffd700, #ff69b4, #ff1493) border-box;
    background-size: 300% 300%;
    padding: 50px;
    text-align: center;
    margin-bottom: 32px;
    transition: all 0.4s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    box-shadow: 
        0 8px 32px rgba(0, 0, 0, 0.3),
        0 0 20px rgba(255, 20, 147, 0.2);
    animation: borderRotate 3s linear infinite;
}

@keyframes borderRotate {
    0% { background-position: 0% 50%; }
    100% { background-position: 100% 50%; }
}

.drop-zone::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s;
}

.drop-zone:hover::before {
    left: 100%;
}

.drop-zone:hover, .drop-zone:focus-within {
    transform: scale(1.03) translateZ(10px);
    box-shadow: 
        0 15px 50px rgba(255, 20, 147, 0.4),
        0 0 40px rgba(138, 43, 226, 0.3),
        0 0 60px rgba(255, 20, 147, 0.2);
    border-width: 5px;
}

.drop-zone-content {
    position: relative;
    z-index: 2;
}

.drop-zone-text strong {
    background: linear-gradient(45deg, #ff1493, #8a2be2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 1.2rem;
    text-shadow: 0 0 20px rgba(255, 20, 147, 0.5);
}

/* Enhanced inputs and textarea with neon focus */
.story-input {
    width: 100%;
    border: 2px solid transparent;
    border-radius: 16px;
    padding: 18px;
    font-size: 1.1rem;
    margin-bottom: 24px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    resize: vertical;
    color: #ffffff;
    box-shadow: 
        inset 0 2px 10px rgba(0, 0, 0, 0.3),
        0 0 10px rgba(255, 20, 147, 0.1);
}

.story-input:focus {
    border: 2px solid #ff1493;
    outline: none;
    background: rgba(255, 255, 255, 0.2);
    box-shadow: 
        0 0 0 4px rgba(255, 20, 147, 0.1),
        inset 0 2px 10px rgba(0, 0, 0, 0.1),
        0 0 20px rgba(255, 20, 147, 0.3),
        0 0 40px rgba(255, 20, 147, 0.1);
}

/* Enhanced tone selection with 3D effects */
.tones {
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
    margin-top: 16px;
}

.tone {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid transparent;
    border-radius: 20px;
    padding: 20px 24px;
    cursor: pointer;
    transition: all 0.4s ease;
    backdrop-filter: blur(15px);
    position: relative;
    overflow: hidden;
    font-size: 1.1rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transform-style: preserve-3d;
}

.tone::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #ff1493, #8a2be2);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.tone.active::before {
    opacity: 1;
}

.tone.active {
    border-color: rgba(255, 20, 147, 0.8);
    color: #fff;
    transform: scale(1.08) translateZ(10px);
    box-shadow: 
        0 12px 35px rgba(255, 20, 147, 0.5),
        0 0 25px rgba(255, 20, 147, 0.4),
        0 0 50px rgba(255, 20, 147, 0.2);
    background: rgba(255, 20, 147, 0.2);
}

.tone:hover:not(.active) {
    border-color: rgba(255, 20, 147, 0.5);
    transform: translateY(-2px) translateZ(5px);
    box-shadow: 
        0 5px 20px rgba(255, 20, 147, 0.2),
        0 0 15px rgba(255, 20, 147, 0.1);
}

/* Enhanced player controls with neon glow */
.player {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
    margin-top: 20px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 0 20px rgba(255, 20, 147, 0.1);
}

.btn-player {
    padding: 12px;
    border-radius: 12px;
    min-width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.btn-player:hover {
    transform: scale(1.1) translateZ(5px);
    box-shadow: 0 0 20px rgba(255, 20, 147, 0.4);
}

.time {
    background: rgba(255, 255, 255, 0.1);
    padding: 8px 16px;
    border-radius: 12px;
    font-weight: 600;
    color: #ffffff;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 0 15px rgba(255, 20, 147, 0.1);
}

/* Enhanced comparison section */
.compare {
    margin-top: 32px;
    padding: 24px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 0 20px rgba(138, 43, 226, 0.1);
}

.compare-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-top: 16px;
}

.compare-col {
    background: rgba(255, 255, 255, 0.05);
    padding: 16px;
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.compare-col:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 20px rgba(255, 20, 147, 0.2);
}

.compare-title {
    font-weight: 600;
    margin-bottom: 12px;
    color: #ff1493;
    text-align: center;
    text-shadow: 0 0 10px rgba(255, 20, 147, 0.5);
}

.compare-text {
    background: rgba(255, 255, 255, 0.1);
    padding: 12px;
    border-radius: 12px;
    font-size: 0.9rem;
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Enhanced file upload section */
.file-upload {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
}

.file-name {
    color: #ff1493;
    font-weight: 600;
    background: rgba(255, 20, 147, 0.1);
    padding: 8px 16px;
    border-radius: 12px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 20, 147, 0.2);
    box-shadow: 0 0 15px rgba(255, 20, 147, 0.2);
}

/* Enhanced inputs row */
.inputs-row {
    display: flex;
    gap: 16px;
    align-items: center;
    flex-wrap: wrap;
    margin-top: 20px;
}

/* Enhanced field labels with neon glow */
.field-label {
    font-weight: 600;
    color: #ffffff;
    margin-bottom: 12px;
    font-size: 1.1rem;
    background: linear-gradient(45deg, #ff1493, #8a2be2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 20px rgba(255, 20, 147, 0.5);
}

/* Enhanced accessibility and skip link */
.skip-link {
    position: absolute;
    left: -999px;
    top: 10px;
    background: linear-gradient(135deg, #ff1493, #8a2be2);
    color: #fff;
    padding: 12px 24px;
    border-radius: 12px;
    z-index: 1000;
    transition: all 0.3s ease;
    text-decoration: none;
    font-weight: 600;
    box-shadow: 0 0 20px rgba(255, 20, 147, 0.4);
}

.skip-link:focus {
    left: 10px;
    transform: scale(1.05);
    box-shadow: 0 4px 20px rgba(255, 20, 147, 0.4);
}

/* Enhanced footer with neon border */
.app-footer {
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff;
    border-radius: 20px;
    padding: 24px 32px;
    margin-top: 40px;
    text-align: center;
    font-size: 1.05rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 
        0 4px 20px rgba(0, 0, 0, 0.3),
        0 0 20px rgba(138, 43, 226, 0.1);
    position: relative;
}

.app-footer::before {
    content: '';
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    background: linear-gradient(45deg, #ff1493, #8a2be2, #00ffff, #ffd700);
    border-radius: 21px;
    z-index: -1;
    opacity: 0.3;
}

/* Enhanced status messages with neon glow */
.sr-status {
    position: fixed;
    top: 20px;
    right: 20px;
    background: linear-gradient(135deg, #ff1493, #8a2be2);
    color: #fff;
    padding: 16px 24px;
    border-radius: 16px;
    box-shadow: 
        0 8px 30px rgba(255, 20, 147, 0.4),
        0 0 30px rgba(255, 20, 147, 0.3);
    backdrop-filter: blur(10px);
    z-index: 1000;
    transform: translateX(400px);
    transition: transform 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.sr-status:not(:empty) {
    transform: translateX(0);
}

/* Add floating action button effect */
.btn-primary {
    position: relative;
}

.btn-primary::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #ff1493, #8a2be2, #00ffff, #ffd700);
    border-radius: 22px;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
    animation: borderGlow 2s ease-in-out infinite;
}

.btn-primary:hover::after {
    opacity: 0.7;
}

@keyframes borderGlow {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 0.8; }
}

/* Enhanced responsive design */
@media (max-width: 768px) {
    body { 
        background-size: 400% 400%, 300% 300%, 200% 200%, 250% 250%, 300% 300%;
    }
    
    .container {
        padding: 20px;
        max-width: 100%;
    }
    
    .panel, .card { 
        padding: 24px; 
        margin-bottom: 24px;
    }
    
    header.app-header { 
        padding: 24px; 
        margin-bottom: 24px;
    }
    
    .title {
        font-size: 2rem;
    }
    
    .compare-grid {
        grid-template-columns: 1fr;
    }
    
    .inputs-row {
        flex-direction: column;
        align-items: stretch;
    }
    
    .btn, button {
        padding: 16px 32px;
        font-size: 1.1rem;
    }
    
    .drop-zone {
        padding: 30px;
    }
}

@media (max-width: 480px) {
    .panel, .card, .container { padding: 16px; }
    
    .title { font-size: 1.8rem; }
    
    .drop-zone { padding: 24px; }
    
    .tones { gap: 12px; }
    
    .tone { padding: 12px 16px; }
}

/* Add floating cursor trail effect */
.cursor-trail {
    position: fixed;
    width: 6px;
    height: 6px;
    background: linear-gradient(45deg, #ff1493, #8a2be2);
    border-radius: 50%;
    pointer-events: none;
    z-index: 9999;
    animation: cursorPulse 1s ease-in-out infinite;
}

@keyframes cursorPulse {
    0%, 100% { 
        transform: scale(1);
        opacity: 0.8;
    }
    50% { 
        transform: scale(1.5);
        opacity: 1;
    }
}

/* Add magnetic effect to buttons */
.btn, button, .tone {
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.btn:hover, button:hover, .tone:hover {
    filter: brightness(1.2) contrast(1.1);
}

/* Add ripple effect to buttons */
.btn, button {
    position: relative;
    overflow: hidden;
}

.btn::after, button::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.btn:active::after, button:active::after {
    width: 300px;
    height: 300px;
}

/* Add floating elements animation */
.panel, .card {
    animation: floatIn 0.8s ease-out;
}

@keyframes floatIn {
    0% {
        opacity: 0;
        transform: translateY(30px) scale(0.9);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Add staggered animation delay for panels */
.panel:nth-child(1) { animation-delay: 0.1s; }
.panel:nth-child(2) { animation-delay: 0.2s; }
.panel:nth-child(3) { animation-delay: 0.3s; }

/* Add text typing effect to title */
.title {
    overflow: hidden;
    border-right: 3px solid #ff1493;
    white-space: nowrap;
    animation: typing 3.5s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes typing {
    from { width: 0; }
    to { width: 100%; }
}

@keyframes blink-caret {
    from, to { border-color: transparent; }
    50% { border-color: #ff1493; }
}

/* Add hover sound effect simulation */
.btn:hover, button:hover, .tone:hover {
    animation: hoverPulse 0.3s ease-in-out;
}

@keyframes hoverPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* Add loading animation for generate button */
.btn-primary.loading {
    position: relative;
    overflow: hidden;
}

.btn-primary.loading::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Add neon text effect to all text elements */
h1, h2, h3, p, span, label {
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

/* Add glass morphism to all interactive elements */
input, textarea, select {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

/* Add floating action button effect to all primary buttons */
.btn-primary, .btn-secondary, .btn-mic {
    position: relative;
    overflow: visible;
}

.btn-primary::before, .btn-secondary::before, .btn-mic::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #ff1493, #8a2be2, #00ffff, #ffd700);
    border-radius: 22px;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.btn-primary:hover::before, .btn-secondary:hover::before, .btn-mic:hover::before {
    opacity: 0.6;
}

/* Add particle explosion effect on button click */
.btn:active, button:active {
    animation: explode 0.3s ease-out;
}

@keyframes explode {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(0.96); }
}

/* Add floating icons animation */
.btn-icon svg, .tone-icon svg {
    transition: all 0.3s ease;
}

.btn:hover .btn-icon svg, .tone:hover .tone-icon svg {
    transform: scale(1.2) rotate(5deg);
    filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.8));
}

/* Add neon border animation to panels */
.panel::after, .card::after {
    content: '';
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    background: linear-gradient(45deg, #ff1493, #8a2be2, #00ffff, #ffd700, #ff1493);
    border-radius: 25px;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.4s ease;
    background-size: 400% 400%;
    animation: borderFlow 3s linear infinite;
}

.panel:hover::after, .card:hover::after {
    opacity: 0.4;
}

@keyframes borderFlow {
    0% { background-position: 0% 50%; }
    100% { background-position: 100% 50%; }
}
