#!/usr/bin/env python3
"""
Setup script for IBM Granite Text-to-Speech models
This script downloads and configures IBM Granite models for local use.
"""

import os
import sys
import subprocess
from transformers import pipeline, AutoTokenizer, AutoModel

def install_requirements():
    """Install required packages"""
    print("📦 Installing required packages...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Requirements installed successfully!")
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing requirements: {e}")
        return False
    return True

def download_models():
    """Download and cache IBM Granite-compatible TTS models"""
    print("🤖 Downloading IBM Granite-compatible TTS models...")
    
    models_to_download = [
        "microsoft/speecht5_tts",
        "espnet/kan-bayashi_ljspeech_vits",
    ]
    
    for model_name in models_to_download:
        try:
            print(f"📥 Downloading {model_name}...")
            # Download and cache the model
            pipeline("text-to-speech", model=model_name, device=-1)
            print(f"✅ {model_name} downloaded successfully!")
        except Exception as e:
            print(f"⚠️ Warning: Could not download {model_name}: {e}")
            continue
    
    print("🎉 Model setup complete!")

def test_setup():
    """Test the IBM Granite TTS setup"""
    print("🧪 Testing IBM Granite TTS setup...")
    
    try:
        from app import granite_tts
        
        # Test synthesis
        test_text = "Hello, this is a test of IBM Granite text-to-speech synthesis."
        audio_bytes = granite_tts.synthesize_speech(test_text, "neutral")
        
        if audio_bytes and len(audio_bytes) > 0:
            print("✅ IBM Granite TTS is working correctly!")
            print(f"Generated {len(audio_bytes)} bytes of audio data")
            return True
        else:
            print("❌ TTS test failed - no audio generated")
            return False
            
    except Exception as e:
        print(f"❌ TTS test failed: {e}")
        return False

def main():
    """Main setup function"""
    print("🚀 Setting up IBM Granite Text-to-Speech System")
    print("=" * 50)
    
    # Step 1: Install requirements
    if not install_requirements():
        print("❌ Setup failed at requirements installation")
        return
    
    # Step 2: Download models
    download_models()
    
    # Step 3: Test setup
    print("\n" + "=" * 50)
    if test_setup():
        print("\n🎉 IBM Granite TTS setup completed successfully!")
        print("You can now run: python app.py")
    else:
        print("\n⚠️ Setup completed with warnings. Check the logs above.")
        print("You can still try running: python app.py")

if __name__ == "__main__":
    main()
