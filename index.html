<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>EchoVerse – AI Audiobook Generator</title>
    <meta name="description" content="Accessible audiobook generator with voice commands, keyboard navigation, and high contrast UI." />

    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;800&display=swap" rel="stylesheet" />
    <link rel="stylesheet" href="AudioBook/static/styles.css" />
  </head>
  <body>
    <a class="skip-link" href="#main" aria-label="Skip to main content">Skip to main</a>

    <header class="app-header" role="banner">
      <div class="logo" aria-hidden="true" title="EchoVerse logo">
        <svg viewBox="0 0 64 64" width="40" height="40" aria-hidden="true">
          <defs>
            <linearGradient id="g" x1="0" x2="1" y1="0" y2="1">
              <stop offset="0%" stop-color="#ff1493" />
              <stop offset="100%" stop-color="#8a2be2" />
            </linearGradient>
          </defs>
          <circle cx="32" cy="32" r="30" fill="url(#g)" />
          <path d="M20 32c0-6 4-10 12-10s12 4 12 10-4 10-12 10-12-4-12-10z" fill="#0b1220" opacity=".15" />
          <rect x="30" y="16" width="4" height="32" rx="2" fill="#0b1220" />
        </svg>
      </div>
      <h1 class="title">EchoVerse – AI Audiobook Generator</h1>
    </header>

    <main id="main" class="container" role="main">
      <section class="panel input-panel" aria-label="Input panel">
        <label for="story" class="field-label">Your text</label>
        <div class="drop-zone" id="dropZone" aria-label="Drop text or PDF files here or click to browse">
          <div class="drop-zone-content">
            <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true">
              <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
              <polyline points="17 8 12 3 7 8" />
              <line x1="12" x2="12" y1="3" y2="15" />
            </svg>
            <div class="drop-zone-text">
              <strong>Drop a .txt or .pdf file here</strong>
              <span>or click to browse</span>
            </div>
          </div>
        </div>
        <textarea
          id="story"
          class="story-input"
          rows="10"
          placeholder="Paste or type your story here..."
          aria-label="Story text area"
        ></textarea>

        <div class="inputs-row">
          <div class="file-upload">
            <input
              type="file"
              id="fileInput"
              accept=".txt,.pdf"
              aria-label="Upload text or PDF file"
            />
            <label for="fileInput" class="btn btn-secondary" tabindex="0" role="button" aria-label="Browse for text file">
              <span class="btn-icon" aria-hidden="true">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
                  <polyline points="17 8 12 3 7 8" />
                  <line x1="12" x2="12" y1="3" y2="15" />
                </svg>
              </span>
              <span>Browse</span>
            </label>
            <span id="fileName" class="file-name" aria-live="polite"></span>
          </div>

          <button id="micButton" class="btn btn-mic" aria-label="Voice command. Press to speak a command." title="Voice command">
            <span class="btn-icon" aria-hidden="true">
              <svg width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M12 1a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z" />
                <path d="M19 10v2a7 7 0 0 1-14 0v-2" />
                <line x1="12" x2="12" y1="19" y2="23" />
                <line x1="8" x2="16" y1="23" y2="23" />
              </svg>
            </span>
            <span class="mic-text">Speak</span>
          </button>
        </div>
      </section>

      <section class="panel tone-panel" aria-label="Tone selection">
        <div class="field-label">Choose a tone</div>
        <div class="tones" role="radiogroup" aria-label="Tone options">
          <button class="tone active" data-tone="neutral" role="radio" aria-checked="true" aria-label="Neutral tone" tabindex="0">
            <span class="tone-icon" aria-hidden="true">
              <svg width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="9" />
                <line x1="8" y1="12" x2="16" y2="12" />
              </svg>
            </span>
            <span class="tone-label">Neutral</span>
          </button>
          <button class="tone" data-tone="suspense" role="radio" aria-checked="false" aria-label="Suspenseful tone" tabindex="-1">
            <span class="tone-icon" aria-hidden="true">
              <svg width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polygon points="12 2 2 7 12 12 22 7 12 2" />
                <polyline points="2 17 12 22 22 17" />
                <polyline points="2 12 12 17 22 12" />
              </svg>
            </span>
            <span class="tone-label">Suspense</span>
          </button>
          <button class="tone" data-tone="inspire" role="radio" aria-checked="false" aria-label="Inspiring tone" tabindex="-1">
            <span class="tone-icon" aria-hidden="true">
              <svg width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M12 2v6" />
                <path d="M5 10a7 7 0 0 1 14 0c0 3.5-2.5 5.5-5 6v3a2 2 0 0 1-4 0v-3c-2.5-.5-5-2.5-5-6z" />
              </svg>
            </span>
            <span class="tone-label">Inspire</span>
          </button>
        </div>
      </section>

      <section class="panel action-panel" aria-label="Generate and playback">
        <button id="generateBtn" class="btn btn-primary" aria-label="Generate audiobook" title="Generate audiobook">
          <span class="btn-icon" aria-hidden="true">
            <svg width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <polygon points="5 3 19 12 5 21 5 3" />
            </svg>
          </span>
          <span>Generate Audiobook</span>
        </button>

        <div class="player" role="group" aria-label="Audio player controls">
          <button id="playBtn" class="btn btn-player" aria-label="Play" title="Play">
            <span aria-hidden="true">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polygon points="5 3 19 12 5 21 5 3" />
              </svg>
            </span>
          </button>
          <button id="pauseBtn" class="btn btn-player" aria-label="Pause" title="Pause">
            <span aria-hidden="true">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <rect x="6" y="4" width="4" height="16" />
                <rect x="14" y="4" width="4" height="16" />
              </svg>
            </span>
          </button>
          <button id="stopBtn" class="btn btn-player" aria-label="Stop" title="Stop">
            <span aria-hidden="true">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <rect x="6" y="6" width="12" height="12" />
              </svg>
            </span>
          </button>
          <div class="time" aria-live="polite" aria-atomic="true">
            <span id="timeCurrent">0:00</span>
            <span class="sep">/</span>
            <span id="timeTotal">0:00</span>
          </div>

          <button id="downloadBtn" class="btn btn-secondary" aria-label="Download audio" title="Download audio (experimental)">
            <span class="btn-icon" aria-hidden="true">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
                <polyline points="7 10 12 15 17 10" />
                <line x1="12" x2="12" y1="15" y2="3" />
              </svg>
            </span>
            <span>Download</span>
          </button>
        </div>
        
        <div class="compare" aria-label="Text comparison" role="region">
          <div class="field-label">Comparison</div>
          <div class="compare-grid">
            <div class="compare-col">
              <div class="compare-title">Original</div>
              <pre id="originalText" class="compare-text" aria-live="polite"></pre>
            </div>
            <div class="compare-col">
              <div class="compare-title">Rewritten</div>
              <pre id="rewrittenText" class="compare-text" aria-live="polite"></pre>
            </div>
          </div>
          <div class="backend-player">
            <button id="playOriginalServer" class="btn btn-secondary" aria-label="Stream original audio from server">Play Server Original</button>
            <button id="playRewrittenServer" class="btn btn-secondary" aria-label="Stream rewritten audio from server">Play Server Rewritten</button>
            <button id="testTTS" class="btn btn-primary" aria-label="Test IBM Watson TTS directly">Test TTS Directly</button>
            <audio id="serverAudio" controls aria-label="Server audio player" style="width:100%; margin-top:8px"></audio>
          </div>
        </div>
      </section>

      <div id="status" class="sr-status" aria-live="polite" aria-atomic="true"></div>
    </main>

    <footer class="app-footer" role="contentinfo">
      <div>
        <strong>Accessibility:</strong> Fully keyboard-navigable, ARIA-labeled controls, high-contrast design, and live voice feedback.
      </div>
      <div>
        Built with love for readers and listeners. Credits: UI by EchoVerse.
      </div>
    </footer>

    <script src="AudioBook/static/script.js"></script>
  </body>
</html>
