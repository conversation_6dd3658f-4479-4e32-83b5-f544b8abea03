# EchoVerse - AI Audiobook Generator

A responsive, accessible web application that generates audiobooks using IBM Watson AI services. Features drag-and-drop file upload, tone-adaptive text rewriting, and high-quality text-to-speech generation.

## Features

- **File Upload**: Support for .txt and .pdf files with drag-and-drop interface
- **Tone Adaptation**: Three AI-powered tones (Neutral, Suspenseful, Inspiring)
- **IBM Watson Integration**: 
  - Watsonx LLM for intelligent text rewriting
  - Watson Text-to-Speech for professional audio generation
- **Accessibility**: Full keyboard navigation, ARIA labels, and screen reader support
- **Voice Commands**: Speech recognition for hands-free operation
- **Responsive Design**: Mobile-friendly interface with modern UI

## Architecture

- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **Backend**: Python Flask with IBM Watson APIs
- **File Processing**: Text extraction from TXT and PDF files
- **Audio Generation**: MP3 output with streaming and download support

## Setup Instructions

### 1. Prerequisites

- Python 3.8+ installed
- IBM Cloud account with Watson services
- Modern web browser (Chrome, Firefox, Safari, Edge)

### 2. IBM Cloud Setup

#### Watsonx.ai (LLM Service)
1. Go to [IBM Cloud Watsonx.ai](https://dataplatform.cloud.ibm.com/)
2. Create a new project
3. Get your API key, project ID, and model ID
4. Note the service URL (usually `https://us-south.ml.cloud.ibm.com`)

#### Watson Text-to-Speech
1. Go to [IBM Cloud TTS](https://cloud.ibm.com/catalog/services/text-to-speech)
2. Create a new service instance
3. Get your API key and service URL
4. Choose a voice (e.g., `en-US_AllisonV3Voice`)

### 3. Backend Setup

#### Option A: Environment Variables (Recommended)
```bash
# Windows PowerShell
$env:WATSONX_API_KEY="your_watsonx_api_key"
$env:WATSONX_URL="https://us-south.ml.cloud.ibm.com"
$env:WATSONX_PROJECT_ID="your_project_id"
$env:WATSONX_MODEL_ID="ibm/granite-13b-chat-v2"
$env:WATSON_TTS_API_KEY="your_tts_api_key"
$env:WATSON_TTS_URL="https://api.us-south.text-to-speech.watson.cloud.ibm.com/instances/your-instance-id"
$env:WATSON_TTS_VOICE="en-US_AllisonV3Voice"

# Windows Command Prompt
set WATSONX_API_KEY=your_watsonx_api_key
set WATSONX_URL=https://us-south.ml.cloud.ibm.com
set WATSONX_PROJECT_ID=your_project_id
set WATSONX_MODEL_ID=ibm/granite-13b-chat-v2
set WATSON_TTS_API_KEY=your_tts_api_key
set WATSON_TTS_URL=https://api.us-south.text-to-speech.watson.cloud.ibm.com/instances/your-instance-id
set WATSON_TTS_VOICE=en-US_AllisonV3Voice
```

#### Option B: Direct Configuration
Edit `app.py` and update the configuration variables at the top.

### 4. Install Dependencies

```bash
# Create virtual environment
python -m venv .venv

# Activate virtual environment
# Windows
.venv\Scripts\activate
# macOS/Linux
source .venv/bin/activate

# Install dependencies
pip install -r requirements_flask.txt
```

### 5. Start the Backend

#### Windows (Recommended)
Double-click `start_flask.bat`

#### Manual Start
```bash
python app.py
```

The Flask server will start at `http://127.0.0.1:5000`

### 6. Frontend Setup

1. Open `index.html` in your web browser
2. Or serve it using a local HTTP server:
   ```bash
   # Python 3
   python -m http.server 8000
   # Then open http://localhost:8000
   ```

## Usage

### 1. Upload a File
- Drag and drop a .txt or .pdf file onto the drop zone
- Or click the drop zone to browse for files
- Supported formats: .txt, .pdf

### 2. Choose Tone
- **Neutral**: Clear, natural narration
- **Suspense**: Dramatic, tension-building style
- **Inspire**: Uplifting, motivational delivery

### 3. Generate Audiobook
- Click "Generate Audiobook"
- The backend will:
  1. Extract text from your file
  2. Send to IBM Watsonx for tone-adaptive rewriting
  3. Generate speech audio using Watson TTS
  4. Return both texts and audio file

### 4. Play and Download
- **Frontend TTS**: Use browser-based text-to-speech
- **Server Audio**: Stream high-quality MP3 from backend
- **Download**: Get the generated MP3 file

## API Endpoints

- `POST /generate` - Main audiobook generation endpoint
- `GET /download/<filename>` - Download generated audio
- `GET /stream/<filename>` - Stream audio for playback
- `GET /health` - Health check

## Troubleshooting

### Common Issues

1. **"API Key Not Set" Error**
   - Check environment variables are set correctly
   - Verify API keys in IBM Cloud console

2. **File Upload Fails**
   - Ensure file is .txt or .pdf
   - Check file size (max 16MB)
   - Verify backend is running

3. **Audio Generation Fails**
   - Check Watson TTS service status
   - Verify TTS API key and URL
   - Check network connectivity

4. **CORS Errors**
   - Backend includes CORS headers
   - Ensure frontend and backend ports match
   - Check browser console for specific errors

### Debug Mode

The Flask backend runs in debug mode by default. Check the terminal output for detailed error messages and processing steps.

## File Structure

```
echoverse/
├── index.html              # Main frontend interface
├── styles.css              # Styling and responsive design
├── script.js               # Frontend JavaScript logic
├── app.py                  # Flask backend server
├── requirements_flask.txt  # Python dependencies
├── start_flask.bat        # Windows startup script
└── README.md              # This file
```

## Browser Compatibility

- **Chrome**: Full support (recommended)
- **Firefox**: Full support
- **Safari**: Full support
- **Edge**: Full support
- **Mobile**: Responsive design with touch support

## Accessibility Features

- **Keyboard Navigation**: Full Tab, Enter, Arrow key support
- **Screen Reader**: ARIA labels and semantic HTML
- **Voice Commands**: Speech recognition for hands-free operation
- **High Contrast**: Dark theme with bright accents
- **Live Feedback**: Voice announcements for status updates

## Performance Notes

- **File Processing**: Large PDFs may take longer to process
- **AI Generation**: Watsonx API response time varies (typically 5-30 seconds)
- **Audio Generation**: TTS processing depends on text length
- **Caching**: Generated audio files are stored temporarily

## Security Considerations

- Files are processed temporarily and cleaned up after processing
- API keys should be kept secure and not committed to version control
- Consider rate limiting for production use
- Implement user authentication for multi-user scenarios

## Support

For issues or questions:
1. Check the troubleshooting section above
2. Verify IBM Cloud service status
3. Check browser console for JavaScript errors
4. Review Flask backend terminal output

## License

This project is provided as-is for educational and development purposes.
