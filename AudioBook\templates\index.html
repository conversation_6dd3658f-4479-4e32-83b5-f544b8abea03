<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>EchoVerse - Professional Text to Speech</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- Google Fonts for professional look -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Three.js for 3D particles -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <style>
        /* Enhanced professional dark background with 3D particles */
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            min-height: 100vh;
            margin: 0;
            padding: 0;
            background:
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.08) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.05) 0%, transparent 50%),
                linear-gradient(135deg, #0a0a1a 0%, #1a1a2e 30%, #16213e 70%, #0f1419 100%);
            color: #e2e8f0;
            line-height: 1.6;
            position: relative;
            overflow-x: hidden;
        }

        /* 3D Particle Canvas */
        #particleCanvas {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -2;
            pointer-events: none;
        }

        /* Subtle animated background elements */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 25% 25%, rgba(99, 102, 241, 0.03) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(168, 85, 247, 0.02) 0%, transparent 50%);
            animation: subtleFloat 20s ease-in-out infinite;
            pointer-events: none;
            z-index: -1;
        }

        @keyframes subtleFloat {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-10px) rotate(1deg); }
        }

        /* 3D Particle interaction effects */
        .particle-interaction {
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            pointer-events: none;
            z-index: 0;
        }

        .container {
            background: rgba(15, 23, 42, 0.85);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(71, 85, 105, 0.2);
            border-radius: 24px;
            box-shadow:
                0 25px 50px -12px rgba(0, 0, 0, 0.25),
                0 0 0 1px rgba(255, 255, 255, 0.05),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            padding: 48px;
            max-width: 850px;
            width: 90%;
            margin: 40px auto;
            display: flex;
            flex-direction: column;
            align-items: center;
            animation: fadeInUp 0.8s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            z-index: 1;
        }

        /* Enhanced container glow effect */
        .container::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg,
                rgba(99, 102, 241, 0.1),
                rgba(168, 85, 247, 0.1),
                rgba(59, 130, 246, 0.1));
            border-radius: 24px;
            z-index: -1;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .container:hover::before {
            opacity: 1;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }
        h2 {
            color: #f8fafc;
            font-weight: 700;
            font-size: 2.75rem;
            margin-bottom: 40px;
            letter-spacing: -0.025em;
            text-align: center;
            background: linear-gradient(135deg, #f8fafc 0%, #cbd5e1 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            position: relative;
            animation: titleGlow 3s ease-in-out infinite alternate;
        }

        @keyframes titleGlow {
            0% { filter: drop-shadow(0 0 10px rgba(248, 250, 252, 0.3)); }
            100% { filter: drop-shadow(0 0 20px rgba(248, 250, 252, 0.5)); }
        }
        .tone-row {
            display: flex;
            gap: 16px;
            margin-bottom: 32px;
            width: 100%;
            justify-content: center;
            flex-wrap: wrap;
        }
        .tone-btn {
            background: rgba(30, 41, 59, 0.6);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(71, 85, 105, 0.3);
            color: #cbd5e1;
            font-weight: 500;
            border-radius: 12px;
            padding: 14px 28px;
            cursor: pointer;
            font-size: 0.95rem;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            outline: none;
            position: relative;
            overflow: hidden;
        }

        .tone-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.5s ease;
        }

        .tone-btn:hover::before {
            left: 100%;
        }

        .tone-btn.active, .tone-btn:focus {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.8), rgba(99, 102, 241, 0.8));
            border-color: rgba(59, 130, 246, 0.6);
            color: #ffffff;
            box-shadow:
                0 8px 25px -8px rgba(59, 130, 246, 0.4),
                0 0 0 1px rgba(59, 130, 246, 0.2);
            transform: translateY(-2px);
        }

        .tone-btn:hover {
            background: rgba(51, 65, 85, 0.8);
            border-color: rgba(100, 116, 139, 0.5);
            transform: translateY(-2px);
            box-shadow: 0 4px 20px -4px rgba(0, 0, 0, 0.3);
        }
        .input-area {
            width: 100%;
            margin-bottom: 32px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        textarea {
            width: 100%;
            min-height: 160px;
            font-size: 1rem;
            margin-bottom: 20px;
            border-radius: 16px;
            border: 1px solid rgba(71, 85, 105, 0.3);
            padding: 20px;
            background: rgba(15, 23, 42, 0.6);
            backdrop-filter: blur(10px);
            color: #e2e8f0;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            resize: vertical;
            font-family: inherit;
            line-height: 1.6;
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        textarea:focus {
            border-color: rgba(59, 130, 246, 0.5);
            outline: none;
            background: rgba(15, 23, 42, 0.8);
            box-shadow:
                0 0 0 3px rgba(59, 130, 246, 0.1),
                0 8px 25px -8px rgba(59, 130, 246, 0.2),
                inset 0 2px 4px rgba(0, 0, 0, 0.1);
            transform: translateY(-1px);
        }

        textarea::placeholder {
            color: #94a3b8;
            font-style: italic;
        }
        /* Enhanced button styles */
        .btn {
            background: rgba(59, 130, 246, 0.9);
            color: #ffffff;
            border: 1px solid rgba(59, 130, 246, 0.6);
            padding: 12px 32px;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            outline: none;
            font-family: inherit;
        }
        
        .btn:hover {
            background: rgba(59, 130, 246, 1);
            transform: translateY(-1px);
            box-shadow: 0 4px 14px 0 rgba(59, 130, 246, 0.25);
        }
        
        .btn:active {
            transform: translateY(0);
        }
        .file-upload {
            width: 100%;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
        }
        .file-upload input[type="file"] {
            display: none;
        }
        .file-upload-btn {
            background: rgba(30, 41, 59, 0.6);
            backdrop-filter: blur(10px);
            color: #cbd5e1;
            border: 1px solid rgba(100, 116, 139, 0.3);
            padding: 12px 24px;
            border-radius: 12px;
            font-size: 0.9rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-family: inherit;
            position: relative;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .file-upload-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.5s ease;
        }

        .file-upload-btn:hover::before {
            left: 100%;
        }

        .file-upload-btn:hover, .file-upload-btn:focus {
            background: rgba(51, 65, 85, 0.8);
            border-color: rgba(148, 163, 184, 0.5);
            transform: translateY(-2px);
            box-shadow: 0 4px 20px -4px rgba(0, 0, 0, 0.3);
        }
        #fileName {
            color: #94a3b8;
            font-size: 0.9rem;
        }
        #generateBtn {
            width: 100%;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.9), rgba(99, 102, 241, 0.9));
            color: #ffffff;
            border: 1px solid rgba(59, 130, 246, 0.4);
            padding: 18px 0;
            border-radius: 16px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            margin-bottom: 28px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            outline: none;
            font-family: inherit;
            position: relative;
            overflow: hidden;
            box-shadow:
                0 8px 25px -8px rgba(59, 130, 246, 0.3),
                0 0 0 1px rgba(255, 255, 255, 0.1);
        }

        #generateBtn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.6s ease;
        }

        #generateBtn:hover::before {
            left: 100%;
        }

        #generateBtn:active {
            transform: translateY(1px);
        }

        #generateBtn:hover, #generateBtn:focus {
            background: linear-gradient(135deg, rgba(59, 130, 246, 1), rgba(99, 102, 241, 1));
            transform: translateY(-2px);
            box-shadow:
                0 12px 35px -8px rgba(59, 130, 246, 0.4),
                0 0 0 1px rgba(255, 255, 255, 0.2);
        }

        #generateBtn:disabled {
            background: rgba(71, 85, 105, 0.4);
            border-color: rgba(71, 85, 105, 0.2);
            color: #94a3b8;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        #generateBtn:disabled::before {
            display: none;
        }
        
        #status {
            color: #cbd5e1;
            margin-bottom: 20px;
            min-height: 28px;
            text-align: center;
            font-size: 0.95rem;
            font-weight: 500;
            padding: 8px 16px;
            border-radius: 8px;
            background: rgba(15, 23, 42, 0.3);
            backdrop-filter: blur(5px);
            border: 1px solid rgba(71, 85, 105, 0.2);
            transition: all 0.3s ease;
        }

        #status:not(:empty) {
            animation: statusFadeIn 0.3s ease-out;
        }

        @keyframes statusFadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .audio-row {
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 16px;
        }
        audio {
            width: 100%;
            border-radius: 16px;
            background: rgba(15, 23, 42, 0.8);
            outline: none;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow:
                0 8px 25px -8px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
        }

        audio:hover {
            transform: translateY(-1px);
            box-shadow:
                0 12px 35px -8px rgba(0, 0, 0, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.15);
        }

        #downloadBtn {
            background: linear-gradient(135deg, rgba(34, 197, 94, 0.9), rgba(16, 185, 129, 0.9));
            color: #ffffff;
            border: 1px solid rgba(34, 197, 94, 0.4);
            padding: 12px 28px;
            border-radius: 12px;
            font-size: 0.95rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            outline: none;
            font-family: inherit;
            position: relative;
            overflow: hidden;
            box-shadow:
                0 4px 14px 0 rgba(34, 197, 94, 0.2),
                0 0 0 1px rgba(255, 255, 255, 0.1);
        }

        #downloadBtn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        #downloadBtn:hover::before {
            left: 100%;
        }

        #downloadBtn:hover, #downloadBtn:focus {
            background: linear-gradient(135deg, rgba(34, 197, 94, 1), rgba(16, 185, 129, 1));
            transform: translateY(-2px);
            box-shadow:
                0 8px 25px -8px rgba(34, 197, 94, 0.4),
                0 0 0 1px rgba(255, 255, 255, 0.2);
        }

        #downloadBtn:active {
            transform: translateY(0);
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 32px 24px;
                margin: 20px auto;
                width: 95%;
            }
            h2 { font-size: 2rem; }
            .tone-row { flex-direction: column; gap: 12px; }
            .tone-btn { width: 100%; }
            #particleCanvas {
                opacity: 0.7; /* Reduce particle intensity on mobile */
            }
        }
    </style>
</head>
<body>
    <!-- 3D Particle Canvas -->
    <canvas id="particleCanvas"></canvas>

    <div class="container">
        <h2>EchoVerse</h2>
        <!-- Tone selection buttons -->
        <div class="tone-row" id="toneRow">
            <button class="tone-btn active" data-tone="neutral" id="toneNeutral">Neutral</button>
            <button class="tone-btn" data-tone="suspense" id="toneSuspense">Suspense</button>
            <button class="tone-btn" data-tone="motivational" id="toneMotivational">Motivational</button>
        </div>
        <!-- Input area: textarea or file upload -->
        <div class="input-area">
            <div class="file-upload">
                <label class="file-upload-btn" for="fileInput">Upload .txt</label>
                <input type="file" id="fileInput" accept=".txt">
                <span id="fileName"></span>
            </div>
            <textarea id="story" placeholder="Type or paste your story here..."></textarea>
        </div>
        <div id="status"></div>
        <button id="generateBtn">Generate</button>
        <div class="audio-row">
            <audio id="audioPlayer" controls style="display:none;"></audio>
            <button id="downloadBtn" style="display:none;">Download Audio</button>
        </div>
    </div>
    <script>
        // --- Tone selection logic with animation ---
        // Keeps track of the selected tone and updates button styles
        const toneRow = document.getElementById('toneRow');
        let selectedTone = 'neutral'; // Default

        toneRow.addEventListener('click', function(e) {
            if (e.target.classList.contains('tone-btn')) {
                // Remove 'active' from all buttons
                document.querySelectorAll('.tone-btn').forEach(btn => btn.classList.remove('active'));
                // Add 'active' to clicked button
                e.target.classList.add('active');
                selectedTone = e.target.getAttribute('data-tone');
            }
        });

        // --- File upload logic for .txt files ---
        const fileInput = document.getElementById('fileInput');
        const fileNameSpan = document.getElementById('fileName');
        const textarea = document.getElementById('story');

        // When a file is selected, read its contents into the textarea
        fileInput.addEventListener('change', function() {
            const file = fileInput.files[0];
            if (file && file.type === "text/plain") {
                fileNameSpan.textContent = file.name;
                const reader = new FileReader();
                reader.onload = function(e) {
                    textarea.value = e.target.result;
                };
                reader.readAsText(file);
            } else {
                fileNameSpan.textContent = '';
                textarea.value = '';
            }
        });


        
        // --- Generate button logic (AI narration logic untouched) ---
        document.getElementById('generateBtn').onclick = async function(e) {
            e.preventDefault();
            const btn = this;
            const audio = document.getElementById('audioPlayer');
            const status = document.getElementById('status');
            const downloadBtn = document.getElementById('downloadBtn');
            const text = textarea.value.trim();
            if (!text) {
                status.textContent = 'Please enter some text or upload a .txt file.';
                return;
            }
            btn.disabled = true;
            status.textContent = "Generating audio...";
            downloadBtn.style.display = "none";
            try {
                const formData = new FormData();
                formData.append('text', text);
                formData.append('tone', selectedTone);  // Send selected tone to IBM Granite backend
                const response = await fetch('/speak', { method: 'POST', body: formData });
                const data = await response.json();

                if (data.success) {
                    if (data.use_frontend_tts) {
                        // Use browser TTS when server TTS is not available
                        status.textContent = data.message || "Using browser TTS...";

                        // Use Web Speech API for TTS
                        if ('speechSynthesis' in window) {
                            const utterance = new SpeechSynthesisUtterance(data.text);

                            // Configure voice based on tone
                            const voices = speechSynthesis.getVoices();
                            if (voices.length > 0) {
                                utterance.voice = voices[0]; // Use first available voice
                            }

                            // Adjust speech parameters based on tone
                            if (selectedTone === 'suspense') {
                                utterance.rate = 0.8;
                                utterance.pitch = 0.8;
                            } else if (selectedTone === 'motivational') {
                                utterance.rate = 1.2;
                                utterance.pitch = 1.1;
                            }

                            speechSynthesis.speak(utterance);
                            status.textContent = "🎤 IBM Granite-enhanced text playing with browser TTS";
                        } else {
                            status.textContent = "Browser TTS not supported. Please install gtts on server.";
                        }

                        textarea.value = "";
                        downloadBtn.style.display = 'none'; // No download for browser TTS

                    } else {
                        // Use server-generated audio file
                        const audioUrl = data.file || data.audio_url;
                        audio.src = audioUrl;
                        audio.style.display = '';
                        audio.load();
                        audio.play();
                        status.textContent = "🎧 IBM Granite-enhanced audio ready!";
                        textarea.value = "";

                        // Show download button and set its link
                        downloadBtn.style.display = '';
                        downloadBtn.onclick = function() {
                            const link = document.createElement('a');
                            link.href = audioUrl;
                            link.download = audioUrl.split('/').pop();
                            document.body.appendChild(link);
                            link.click();
                            document.body.removeChild(link);
                        };
                    }
                } else {
                    status.textContent = data.error || "Failed to generate audio.";
                }
            } catch (err) {
                status.textContent = "Error contacting server.";
            } finally {
                btn.disabled = false;
            }
        };

        // Focus textarea on load for fast typing
        textarea.focus();

        // === 3D PARTICLE SYSTEM ===
        class ParticleSystem {
            constructor() {
                this.canvas = document.getElementById('particleCanvas');
                this.scene = new THREE.Scene();
                this.camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
                this.renderer = new THREE.WebGLRenderer({ canvas: this.canvas, alpha: true, antialias: true });
                this.particles = [];
                this.mouse = { x: 0, y: 0 };
                this.time = 0;

                this.init();
                this.createParticles();
                this.animate();
                this.setupEventListeners();
            }

            init() {
                this.renderer.setSize(window.innerWidth, window.innerHeight);
                this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
                this.camera.position.z = 5;

                // Add subtle fog for depth
                this.scene.fog = new THREE.Fog(0x0a0a1a, 10, 50);
            }

            createParticles() {
                const particleCount = window.innerWidth < 768 ? 150 : 300;
                const geometry = new THREE.BufferGeometry();
                const positions = new Float32Array(particleCount * 3);
                const colors = new Float32Array(particleCount * 3);
                const sizes = new Float32Array(particleCount);

                // Professional color palette
                const colorPalette = [
                    new THREE.Color(0.23, 0.51, 0.96), // Blue
                    new THREE.Color(0.39, 0.40, 0.95), // Indigo
                    new THREE.Color(0.66, 0.33, 0.97), // Purple
                    new THREE.Color(0.13, 0.77, 0.59), // Emerald
                    new THREE.Color(0.96, 0.96, 0.97), // White
                ];

                for (let i = 0; i < particleCount; i++) {
                    // Position particles in a professional scattered pattern
                    positions[i * 3] = (Math.random() - 0.5) * 20;
                    positions[i * 3 + 1] = (Math.random() - 0.5) * 20;
                    positions[i * 3 + 2] = (Math.random() - 0.5) * 10;

                    // Assign colors from palette
                    const color = colorPalette[Math.floor(Math.random() * colorPalette.length)];
                    colors[i * 3] = color.r;
                    colors[i * 3 + 1] = color.g;
                    colors[i * 3 + 2] = color.b;

                    // Vary particle sizes subtly
                    sizes[i] = Math.random() * 2 + 0.5;
                }

                geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
                geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
                geometry.setAttribute('size', new THREE.BufferAttribute(sizes, 1));

                // Professional particle material with subtle glow
                const material = new THREE.ShaderMaterial({
                    uniforms: {
                        time: { value: 0 },
                        mouse: { value: new THREE.Vector2() }
                    },
                    vertexShader: `
                        attribute float size;
                        attribute vec3 color;
                        varying vec3 vColor;
                        varying float vAlpha;
                        uniform float time;
                        uniform vec2 mouse;

                        void main() {
                            vColor = color;

                            vec3 pos = position;

                            // Subtle floating animation
                            pos.y += sin(time * 0.5 + position.x * 0.1) * 0.3;
                            pos.x += cos(time * 0.3 + position.y * 0.1) * 0.2;

                            // Mouse interaction (subtle)
                            vec2 mouseInfluence = (mouse - vec2(pos.x, pos.y)) * 0.1;
                            pos.xy += mouseInfluence * 0.5;

                            // Distance-based alpha for depth
                            float distance = length(pos);
                            vAlpha = 1.0 - (distance / 15.0);
                            vAlpha = clamp(vAlpha, 0.1, 0.8);

                            vec4 mvPosition = modelViewMatrix * vec4(pos, 1.0);
                            gl_Position = projectionMatrix * mvPosition;
                            gl_PointSize = size * (300.0 / -mvPosition.z);
                        }
                    `,
                    fragmentShader: `
                        varying vec3 vColor;
                        varying float vAlpha;

                        void main() {
                            // Create soft circular particles
                            vec2 center = gl_PointCoord - vec2(0.5);
                            float distance = length(center);

                            if (distance > 0.5) discard;

                            // Soft edge with professional glow
                            float alpha = 1.0 - (distance * 2.0);
                            alpha = smoothstep(0.0, 1.0, alpha);

                            // Professional subtle glow effect
                            float glow = 1.0 - distance;
                            glow = pow(glow, 2.0);

                            gl_FragColor = vec4(vColor, alpha * vAlpha * 0.6 + glow * 0.2);
                        }
                    `,
                    transparent: true,
                    blending: THREE.AdditiveBlending,
                    depthWrite: false
                });

                this.particleSystem = new THREE.Points(geometry, material);
                this.scene.add(this.particleSystem);
            }

            setupEventListeners() {
                // Mouse movement for subtle interaction
                document.addEventListener('mousemove', (event) => {
                    this.mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
                    this.mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;
                });

                // Window resize
                window.addEventListener('resize', () => {
                    this.camera.aspect = window.innerWidth / window.innerHeight;
                    this.camera.updateProjectionMatrix();
                    this.renderer.setSize(window.innerWidth, window.innerHeight);
                });

                // Reduce particles on mobile for performance
                if (window.innerWidth < 768) {
                    this.canvas.style.opacity = '0.7';
                }
            }

            animate() {
                requestAnimationFrame(() => this.animate());

                this.time += 0.01;

                // Update shader uniforms
                if (this.particleSystem.material.uniforms) {
                    this.particleSystem.material.uniforms.time.value = this.time;
                    this.particleSystem.material.uniforms.mouse.value.set(this.mouse.x, this.mouse.y);
                }

                // Subtle rotation for professional movement
                this.particleSystem.rotation.y += 0.001;
                this.particleSystem.rotation.x += 0.0005;

                this.renderer.render(this.scene, this.camera);
            }
        }

        // Initialize particle system when page loads
        window.addEventListener('load', () => {
            new ParticleSystem();
        });
    </script>
</body>
</html>