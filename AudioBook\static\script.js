/*
  EchoVerse – Accessible Audiobook Generator
  - High-contrast UI with keyboard navigation and ARIA
  - Text input and .txt file upload
  - Tone selection: neutral | suspense | inspire
  - Voice commands via Web Speech API (if available)
  - TTS playback using SpeechSynthesis with live status announcements
  - Flask backend integration for IBM Watson processing
*/

(function () {
  const storyEl = document.getElementById('story');
  const fileInputEl = document.getElementById('fileInput');
  const fileLabelEl = document.querySelector('label[for="fileInput"]');
  const fileNameEl = document.getElementById('fileName');
  const micButtonEl = document.getElementById('micButton');
  const generateBtnEl = document.getElementById('generateBtn');
  const playBtnEl = document.getElementById('playBtn');
  const pauseBtnEl = document.getElementById('pauseBtn');
  const stopBtnEl = document.getElementById('stopBtn');
  const downloadBtnEl = document.getElementById('downloadBtn');
  const statusEl = document.getElementById('status');
  const timeCurrentEl = document.getElementById('timeCurrent');
  const timeTotalEl = document.getElementById('timeTotal');
  const originalTextEl = document.getElementById('originalText');
  const rewrittenTextEl = document.getElementById('rewrittenText');
  const playOriginalServerEl = document.getElementById('playOriginalServer');
  const playRewrittenServerEl = document.getElementById('playRewrittenServer');
  const testTTSEl = document.getElementById('testTTS');
  const serverAudioEl = document.getElementById('serverAudio');
  const dropZoneEl = document.getElementById('dropZone');

  const toneButtons = Array.from(document.querySelectorAll('.tone'));
  let selectedTone = 'neutral';
  let voices = [];
  let currentUtterance = null;
  let isGenerated = false;
  let synthInterval = null;
  let spokenTextCache = '';
  let currentFile = null; // Store the current uploaded file

  // Flask backend configuration
  const FLASK_BACKEND = 'http://127.0.0.1:5000';

  function setStatus(text, speak = false) {
    statusEl.textContent = text;
    if (speak) speakText(text, { announce: true });
  }

  function loadVoices() {
    voices = window.speechSynthesis ? window.speechSynthesis.getVoices() : [];
  }

  if (window.speechSynthesis) {
    loadVoices();
    window.speechSynthesis.onvoiceschanged = loadVoices;
  }

  // -------- Drag and Drop functionality --------
  function handleDragOver(e) {
    e.preventDefault();
    e.stopPropagation();
    dropZoneEl.classList.add('drag-over');
  }

  function handleDragLeave(e) {
    e.preventDefault();
    e.stopPropagation();
    dropZoneEl.classList.remove('drag-over');
  }

  function handleDrop(e) {
    e.preventDefault();
    e.stopPropagation();
    dropZoneEl.classList.remove('drag-over');
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      const file = files[0];
      if (file.type === 'text/plain' || file.name.endsWith('.txt') || file.name.endsWith('.pdf')) {
        handleFileUpload(file);
      } else {
        setStatus('Please drop a .txt or .pdf file.', true);
      }
    }
  }

  function handleFileUpload(file) {
    currentFile = file; // Store the file for backend processing
    fileNameEl.textContent = file.name;
    
    // For text files, also load into textarea for preview
    if (file.name.endsWith('.txt')) {
      const reader = new FileReader();
      reader.onload = function(e) {
        storyEl.value = e.target.result;
        setStatus('Text file loaded successfully.', true);
      };
      reader.onerror = function() {
        setStatus('Failed to read text file.', true);
      };
      reader.readAsText(file);
    } else if (file.name.endsWith('.pdf')) {
      storyEl.value = '[PDF file uploaded - text will be extracted by backend]';
      setStatus('PDF file loaded successfully.', true);
    }
  }

  // Attach drag and drop event listeners
  dropZoneEl.addEventListener('dragover', handleDragOver);
  dropZoneEl.addEventListener('dragenter', handleDragOver);
  dropZoneEl.addEventListener('dragleave', handleDragLeave);
  dropZoneEl.addEventListener('drop', handleDrop);
  
  // Click on drop zone to trigger file input
  dropZoneEl.addEventListener('click', () => {
    fileInputEl.click();
  });

  // Tone selection with keyboard support
  function updateToneSelection(button) {
    toneButtons.forEach((btn) => {
      const isActive = btn === button;
      btn.classList.toggle('active', isActive);
      btn.setAttribute('aria-checked', String(isActive));
      btn.tabIndex = isActive ? 0 : -1;
    });
    selectedTone = button.getAttribute('data-tone') || 'neutral';
  }

  toneButtons.forEach((btn, index) => {
    btn.addEventListener('click', () => updateToneSelection(btn));
    btn.addEventListener('keydown', (e) => {
      if (e.key === 'ArrowRight' || e.key === 'ArrowDown') {
        e.preventDefault();
        const next = toneButtons[(index + 1) % toneButtons.length];
        updateToneSelection(next);
        next.focus();
      } else if (e.key === 'ArrowLeft' || e.key === 'ArrowUp') {
        e.preventDefault();
        const prev = toneButtons[(index - 1 + toneButtons.length) % toneButtons.length];
        updateToneSelection(prev);
        updateToneSelection(prev);
      } else if (e.key === ' ' || e.key === 'Enter') {
        e.preventDefault();
        updateToneSelection(btn);
      }
    });
  });

  // File upload handling
  fileInputEl.addEventListener('change', async (e) => {
    const file = e.target.files && e.target.files[0];
    if (!file) return;
    handleFileUpload(file);
  });

  // Ensure label acts like a button via keyboard
  if (fileLabelEl) {
    fileLabelEl.addEventListener('keydown', (e) => {
      if (e.key === 'Enter' || e.key === ' ') {
        e.preventDefault();
        fileInputEl.click();
      }
    });
  }

  // Generate audiobook with Flask backend
  generateBtnEl.addEventListener('click', handleGenerate);
  generateBtnEl.addEventListener('keydown', (e) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      handleGenerate();
    }
  });

  async function handleGenerate() {
    if (!currentFile) {
      setStatus('Please upload a file first.', true);
      return;
    }

    // Get the text content to read immediately
    let textToRead = '';
    if (currentFile.name.endsWith('.txt')) {
      textToRead = storyEl.value || '';
    } else if (currentFile.name.endsWith('.pdf')) {
      textToRead = storyEl.value || '[PDF file uploaded - text will be extracted by backend]';
    }

    if (!textToRead || textToRead.trim() === '') {
      setStatus('No text content to read.', true);
      return;
    }

    // Show loading state
    generateBtnEl.disabled = true;
    generateBtnEl.textContent = 'Generating...';
    setStatus('Starting to read your story...', true);

    // Immediately start reading the story with browser TTS
    spokenTextCache = textToRead;
    isGenerated = true;
    
    // Start reading the story right away
    speakText(textToRead);
    setStatus('Reading your story aloud!', true);

    // Also process with backend for enhanced features (in background)
    try {
      // Create FormData for file upload
      const formData = new FormData();
      formData.append('file', currentFile);
      formData.append('tone', selectedTone);

      console.log('Sending request to Flask backend...');
      
      // Send to Flask backend (this runs in background)
      const response = await fetch(`${FLASK_BACKEND}/generate`, {
        method: 'POST',
        body: formData
      });

      console.log('Backend response status:', response.status);

      if (response.ok) {
        const data = await response.json();
        console.log('Backend response data:', data);
        
        if (data.success) {
          // Update UI with backend results
          originalTextEl.textContent = data.original_text || '';
          rewrittenTextEl.textContent = data.rewritten_text || '';
          
          // Set up server audio controls with proper audio handling
          playOriginalServerEl.onclick = async () => {
            try {
              console.log('Playing original audio from server...');
              const audioResponse = await fetch(`${FLASK_BACKEND}${data.audio_file.stream_url}`);
              if (audioResponse.ok) {
                const audioBlob = await audioResponse.blob();
                const audioUrl = URL.createObjectURL(audioBlob);
                
                // Create new audio element and play
                const audio = new Audio(audioUrl);
                audio.onended = () => URL.revokeObjectURL(audioUrl);
                audio.onerror = (e) => {
                  console.error('Audio playback error:', e);
                  setStatus('Error playing audio from server.', true);
                };
                
                // Play the audio (user-triggered)
                await audio.play();
                setStatus('Playing original audio from IBM Watson TTS!', true);
              } else {
                console.error('Failed to fetch audio:', audioResponse.status);
                setStatus('Failed to load audio from server.', true);
              }
            } catch (error) {
              console.error('Error playing original audio:', error);
              setStatus('Error playing audio from server.', true);
            }
          };
          
          playRewrittenServerEl.onclick = async () => {
            try {
              console.log('Playing rewritten audio from server...');
              const audioResponse = await fetch(`${FLASK_BACKEND}${data.audio_file.stream_url}`);
              if (audioResponse.ok) {
                const audioBlob = await audioResponse.blob();
                const audioUrl = URL.createObjectURL(audioBlob);
                
                // Create new audio element and play
                const audio = new Audio(audioUrl);
                audio.onended = () => URL.revokeObjectURL(audioUrl);
                audio.onerror = (e) => {
                  console.error('Audio playback error:', e);
                  setStatus('Error playing audio from server.', true);
                };
                
                // Play the audio (user-triggered)
                await audio.play();
                setStatus('Playing rewritten audio from IBM Watson TTS!', true);
              } else {
                console.error('Failed to fetch audio:', audioResponse.status);
                setStatus('Failed to load audio from server.', true);
              }
            } catch (error) {
              console.error('Error playing rewritten audio:', error);
              setStatus('Error playing audio from server.', true);
            }
          };

          // Announce backend completion
          setStatus('AI-enhanced version ready! You can now use server controls for better quality audio.', true);
          
          // Update spoken text cache to the rewritten version
          spokenTextCache = data.rewritten_text;
          
        } else {
          console.log('Backend processing completed but no enhanced features available');
        }
      } else {
        console.error('Backend processing failed:', response.status, response.statusText);
        const errorText = await response.text();
        console.error('Error details:', errorText);
        setStatus('Backend processing failed, continuing with browser TTS', true);
      }

    } catch (error) {
      console.error('Backend processing error:', error);
      setStatus('Backend processing error, continuing with browser TTS', true);
      // Don't show error to user since browser TTS is working
    } finally {
      // Reset button state
      generateBtnEl.disabled = false;
      generateBtnEl.innerHTML = '<span class="btn-icon" aria-hidden="true"><svg width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polygon points="5 3 19 12 5 21 5 3" /></svg></span><span>Generate Audiobook</span>';
    }
  }

  // Direct TTS test button functionality
  testTTSEl.addEventListener('click', async () => {
    try {
      const testText = "Hello! This is a test of IBM Watson Text-to-Speech. If you can hear this, the TTS integration is working correctly.";
      
      setStatus('Testing IBM Watson TTS...', true);
      console.log('Testing TTS with text:', testText);
      
      // Send test request to backend
      const response = await fetch(`${FLASK_BACKEND}/test-tts`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text: testText,
          voice: 'en-US_AllisonV3Voice'
        })
      });

      if (response.ok) {
        const audioBlob = await response.blob();
        const audioUrl = URL.createObjectURL(audioBlob);
        
        // Create and play audio
        const audio = new Audio(audioUrl);
        audio.onended = () => URL.revokeObjectURL(audioUrl);
        audio.onerror = (e) => {
          console.error('TTS test audio error:', e);
          setStatus('TTS test failed - audio error', true);
        };
        
        await audio.play();
        setStatus('TTS test successful! Playing test audio...', true);
        console.log('TTS test audio playing successfully');
      } else {
        const errorText = await response.text();
        console.error('TTS test failed:', response.status, errorText);
        setStatus('TTS test failed - check console for details', true);
      }
    } catch (error) {
      console.error('TTS test error:', error);
      setStatus('TTS test error - check console for details', true);
    }
  });

  function buildTonePreface(tone) {
    switch (tone) {
      case 'suspense':
        return 'Narrate with a suspenseful tone, steady pace, and dramatic pauses. ';
      case 'inspire':
        return 'Narrate with an inspiring, uplifting tone and energetic cadence. ';
      default:
        return 'Narrate with a clear, neutral tone and natural pace. ';
    }
  }

  // TTS helpers
  function createUtterance(text, tone) {
    const utterance = new SpeechSynthesisUtterance(text);
    // Choose a voice; prefer a local English voice if available
    if (voices && voices.length) {
      const preferred = voices.find((v) => /en/i.test(v.lang) && v.localService) || voices.find((v) => /en/i.test(v.lang));
      if (preferred) utterance.voice = preferred;
    }
    
    // Apply tone-specific settings for better story narration
    if (tone === 'suspense') {
      utterance.rate = 0.85;        // Slower for dramatic effect
      utterance.pitch = 0.8;         // Lower pitch for suspense
      utterance.volume = 0.9;        // Slightly lower volume
    } else if (tone === 'inspire') {
      utterance.rate = 1.1;          // Slightly faster for energy
      utterance.pitch = 1.2;         // Higher pitch for enthusiasm
      utterance.volume = 1.0;        // Full volume
    } else { // neutral
      utterance.rate = 1.0;          // Natural pace
      utterance.pitch = 1.0;         // Natural pitch
      utterance.volume = 1.0;        // Full volume
    }
    
    return utterance;
  }

  function speakText(text, options = {}) {
    if (!('speechSynthesis' in window)) {
      setStatus('Speech Synthesis not supported in this browser.', false);
      return;
    }
    
    // Stop any current speech
    window.speechSynthesis.cancel();
    
    // Don't add tone preface to the actual text - just apply tone settings
    const utterance = createUtterance(text, selectedTone);
    currentUtterance = utterance;

    utterance.onstart = () => {
      startTicker();
      setStatus(`Reading in ${selectedTone} tone: "${text.substring(0, 50)}${text.length > 50 ? '...' : ''}"`, false);
    };
    
    utterance.onend = () => {
      stopTicker();
      updateTimeDisplay(0, 0);
      setStatus('Finished reading your story!', true);
    };
    
    utterance.onerror = () => {
      stopTicker();
      setStatus('Error during speech synthesis.', true);
    };

    // Start reading immediately
    window.speechSynthesis.speak(utterance);
  }

  function highlightCurrentWord(currentWordIndex) {
    const text = storyEl.value;
    if (!text) return;
    
    const words = text.split(' ');
    if (currentWordIndex >= words.length) return;
    
    // Create a visual indicator of current reading position
    const beforeWords = words.slice(0, currentWordIndex).join(' ');
    const currentWord = words[currentWordIndex];
    const afterWords = words.slice(currentWordIndex + 1).join(' ');
    
    // Update textarea with highlighted current word
    const highlightedText = beforeWords + (beforeWords ? ' ' : '') + 
                           `[${currentWord}]` + 
                           (afterWords ? ' ' : '') + afterWords;
    
    // Only update if text has changed to avoid cursor jumping
    if (storyEl.value !== highlightedText) {
      storyEl.value = highlightedText;
    }
  }

  function updateReadingStatus(currentText, position) {
    if (currentText && position !== undefined) {
      const words = currentText.split(' ');
      const currentWord = Math.floor(position / 12); // Approximate words per second
      const totalWords = words.length;
      
      if (currentWord < totalWords) {
        const currentWordText = words[currentWord] || '';
        const context = words.slice(Math.max(0, currentWord - 2), currentWord + 3).join(' ');
        setStatus(`Reading: "${context}..." (${currentWord + 1}/${totalWords} words)`, false);
        
        // Highlight current word in textarea
        highlightCurrentWord(currentWord);
      }
    }
  }

  function startTicker() {
    const start = Date.now();
    clearInterval(synthInterval);
    synthInterval = setInterval(() => {
      const elapsed = (Date.now() - start) / 1000;
      // We do not know total duration; approximate with characters length
      const total = Math.max(5, Math.min(600, Math.round(spokenTextCache.length / 12)));
      updateTimeDisplay(elapsed, total);
      
      // Update reading status
      updateReadingStatus(spokenTextCache, elapsed);
    }, 250);
  }

  function restoreOriginalText() {
    if (currentFile && currentFile.name.endsWith('.txt')) {
      const reader = new FileReader();
      reader.onload = function(e) {
        storyEl.value = e.target.result;
      };
      reader.readAsText(currentFile);
    } else if (currentFile && currentFile.name.endsWith('.pdf')) {
      storyEl.value = '[PDF file uploaded - text will be extracted by backend]';
    }
  }

  function stopTicker() {
    clearInterval(synthInterval);
    synthInterval = null;
    
    // Restore original text when reading stops
    restoreOriginalText();
  }

  function updateTimeDisplay(secCurrent, secTotal) {
    timeCurrentEl.textContent = formatTime(Math.max(0, Math.min(secTotal, secCurrent)));
    timeTotalEl.textContent = formatTime(secTotal);
  }

  function formatTime(seconds) {
    const s = Math.max(0, Math.round(seconds));
    const m = Math.floor(s / 60);
    const r = s % 60;
    return `${m}:${r.toString().padStart(2, '0')}`;
  }

  // Player controls
  playBtnEl.addEventListener('click', playFromStart);
  pauseBtnEl.addEventListener('click', () => {
    if ('speechSynthesis' in window && window.speechSynthesis.speaking && !window.speechSynthesis.paused) {
      window.speechSynthesis.pause();
      setStatus('Paused.', false);
    }
  });
  stopBtnEl.addEventListener('click', () => {
    if ('speechSynthesis' in window) {
      window.speechSynthesis.cancel();
      stopTicker();
      updateTimeDisplay(0, 0);
      setStatus('Stopped.', false);
    }
  });

  function playFromStart() {
    if (!isGenerated) {
      // If not generated yet, try to generate first
      if (currentFile) {
        handleGenerate();
        return;
      } else {
        setStatus('Please upload a file first.', true);
        return;
      }
    }
    
    const text = (storyEl.value || '').trim();
    if (!text) return;
    
    if ('speechSynthesis' in window) {
      // If paused, resume
      if (window.speechSynthesis.paused) {
        window.speechSynthesis.resume();
        setStatus('Resumed reading.', false);
      } else {
        // Start reading from beginning
        speakText(text);
        setStatus('Reading your story.', false);
      }
    }
  }

  // Voice command support
  const supportsRec = 'webkitSpeechRecognition' in window || 'SpeechRecognition' in window;
  let recognition = null;
  if (supportsRec) {
    const Rec = window.SpeechRecognition || window.webkitSpeechRecognition;
    recognition = new Rec();
    recognition.lang = 'en-US';
    recognition.continuous = false;
    recognition.interimResults = false;

    recognition.onstart = () => {
      micButtonEl.classList.add('listening');
      setStatus('Listening for command...', true);
    };
    recognition.onend = () => {
      micButtonEl.classList.remove('listening');
      setStatus('Stopped listening.', false);
    };
    recognition.onerror = () => {
      micButtonEl.classList.remove('listening');
      setStatus('Voice recognition error.', true);
    };
    recognition.onresult = (event) => {
      const transcript = Array.from(event.results)
        .map((r) => r[0]?.transcript)
        .join(' ')
        .toLowerCase();
      handleVoiceCommand(transcript);
    };
  }

  micButtonEl.addEventListener('click', () => {
    if (!recognition) {
      setStatus('Voice recognition not supported in this browser.', true);
      return;
    }
    try {
      recognition.start();
    } catch (_) {
      // ignore repeated start errors
    }
  });

  function handleVoiceCommand(text) {
    // Simple intents
    if (text.includes('suspense')) {
      updateToneSelection(document.querySelector('.tone[data-tone="suspense"]'));
      setStatus('Tone set to suspenseful.', true);
    } else if (text.includes('inspire') || text.includes('inspiring')) {
      updateToneSelection(document.querySelector('.tone[data-tone="inspire"]'));
      setStatus('Tone set to inspiring.', true);
    } else if (text.includes('neutral')) {
      updateToneSelection(document.querySelector('.tone[data-tone="neutral"]'));
      setStatus('Tone set to neutral.', true);
    }

    if (text.includes('upload') || text.includes('file')) {
      fileInputEl.focus();
      setStatus('Please choose a text file to upload.', true);
    }
    if (text.includes('generate') || text.includes('read') || text.includes('start')) {
      handleGenerate();
    }
    if (text.includes('play') || text.includes('continue')) {
      playFromStart();
    }
    if (text.includes('pause')) {
      if ('speechSynthesis' in window) window.speechSynthesis.pause();
      setStatus('Paused reading.', true);
    }
    if (text.includes('stop') || text.includes('end')) {
      if ('speechSynthesis' in window) window.speechSynthesis.cancel();
      setStatus('Stopped reading.', true);
    }
    if (text.includes('slower') || text.includes('slow down')) {
      if (currentUtterance) {
        currentUtterance.rate = Math.max(0.5, (currentUtterance.rate || 1) - 0.1);
        setStatus('Reading slower.', true);
      }
    }
    if (text.includes('faster') || text.includes('speed up')) {
      if (currentUtterance) {
        currentUtterance.rate = Math.min(2.0, (currentUtterance.rate || 1) + 0.1);
        setStatus('Reading faster.', true);
      }
    }
  }

  // Download functionality
  downloadBtnEl.addEventListener('click', async () => {
    if (!currentFile) {
      setStatus('Please upload a file first.', true);
      return;
    }

    // Check if we have backend audio available
    try {
      const response = await fetch(`${FLASK_BACKEND}/health`);
      if (response.ok) {
        // Backend is available - check if we have generated audio
        if (rewrittenTextEl.textContent && rewrittenTextEl.textContent.trim() !== '') {
          setStatus('Downloading high-quality MP3 from backend...', true);
          // Trigger download of the backend audio
          const downloadLink = document.createElement('a');
          downloadLink.href = `${FLASK_BACKEND}/download/audiobook_${Date.now()}.mp3`;
          downloadLink.download = `audiobook_${selectedTone}_${currentFile.name.replace(/\.[^/.]+$/, '')}.mp3`;
          document.body.appendChild(downloadLink);
          downloadLink.click();
          document.body.removeChild(downloadLink);
          setStatus('Download started! Check your downloads folder.', true);
        } else {
          setStatus('Generate audiobook first to download MP3, or use text download.', true);
        }
        return;
      }
    } catch (_) {
      // Backend not available, continue with text download
    }

    // Fallback: download text content
    const text = storyEl.value || '';
    if (!text) {
      setStatus('Nothing to download. Please enter text first.', true);
      return;
    }

    // Create a formatted text file with tone information
    const downloadText = `EchoVerse Audiobook - ${selectedTone.toUpperCase()} Tone\n` +
                        `Generated on: ${new Date().toLocaleString()}\n` +
                        `File: ${currentFile.name}\n` +
                        `\n${'='.repeat(50)}\n\n` +
                        text;
    
    const blob = new Blob([downloadText], { type: 'text/plain;charset=utf-8' });
    const filename = `audiobook_${selectedTone}_${currentFile.name.replace(/\.[^/.]+$/, '')}.txt`;
    triggerDownload(blob, filename);
    setStatus(`Downloaded: ${filename}`, true);
  });

  function triggerDownload(blob, filename) {
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    a.remove();
    setTimeout(() => URL.revokeObjectURL(url), 1000);
  }

  // Send text to Flask and play the returned audio
  async function generateAndPlay() {
    const text = document.getElementById('story').value;
    const formData = new FormData();
    formData.append('text', text);

    const response = await fetch('/speak', { method: 'POST', body: formData });
    const data = await response.json();
    if (data.audio_url) {
        const audio = document.getElementById('serverAudio');
        audio.src = data.audio_url;
        audio.load();
        audio.play();
    } else {
        alert(data.error || 'Failed to generate audio.');
    }
}
document.getElementById('generateBtn').addEventListener('click', generateAndPlay);
})();
document.addEventListener('DOMContentLoaded', function () {
    const btn = document.getElementById('generateBtn');
    const textarea = document.getElementById('story');
    const audio = document.getElementById('serverAudio');

    btn.addEventListener('click', async function (e) {
        e.preventDefault(); // Prevent form submit/page refresh

        const text = textarea.value.trim();
        if (!text) {
            alert('Please enter or paste a story.');
            return;
        }

        btn.disabled = true;
        btn.textContent = "Generating...";

        const formData = new FormData();
        formData.append('text', text);

        try {
            const response = await fetch('/speak', {
                method: 'POST',
                body: formData
            });
            const data = await response.json();

            if (data.audio_url) {
                audio.src = data.audio_url;
                audio.load();
                audio.play();
            } else {
                alert(data.error || "Failed to generate audio.");
            }
        } catch (err) {
            alert("Error contacting server.");
        } finally {
            btn.disabled = false;
            btn.textContent = "Generate Audiobook";
        }
    });
});

// Add cursor trail effect
function createCursorTrail() {
    const trail = document.createElement('div');
    trail.className = 'cursor-trail';
    document.body.appendChild(trail);
    
    let mouseX = 0, mouseY = 0;
    let trailX = 0, trailY = 0;
    
    document.addEventListener('mousemove', (e) => {
        mouseX = e.clientX;
        mouseY = e.clientY;
    });
    
    function animateTrail() {
        trailX += (mouseX - trailX) * 0.1;
        trailY += (mouseY - trailY) * 0.1;
        
        trail.style.left = trailX - 3 + 'px';
        trail.style.top = trailY - 3 + 'px';
        
        requestAnimationFrame(animateTrail);
    }
    
    animateTrail();
}

// Add enhanced button effects
function enhanceButtons() {
    const buttons = document.querySelectorAll('.btn, button, .tone');
    
    buttons.forEach(button => {
        // Add ripple effect
        button.addEventListener('click', function(e) {
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;
            
            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('ripple');
            
            this.appendChild(ripple);
            
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
        
        // Add magnetic effect
        button.addEventListener('mousemove', function(e) {
            const rect = this.getBoundingClientRect();
            const x = e.clientX - rect.left - rect.width / 2;
            const y = e.clientY - rect.top - rect.height / 2;
            
            this.style.transform = `translate(${x * 0.1}px, ${y * 0.1}px)`;
        });
        
        button.addEventListener('mouseleave', function() {
            this.style.transform = '';
        });
    });
}

// Add floating particles effect
function createFloatingParticles() {
    const particleCount = 20;
    
    for (let i = 0; i < particleCount; i++) {
        const particle = document.createElement('div');
        particle.className = 'floating-particle';
        particle.style.cssText = `
            position: fixed;
            width: ${Math.random() * 4 + 2}px;
            height: ${Math.random() * 4 + 2}px;
            background: ${['#ff1493', '#8a2be2', '#00ffff', '#ffd700'][Math.floor(Math.random() * 4)]};
            border-radius: 50%;
            left: ${Math.random() * 100}vw;
            top: ${Math.random() * 100}vh;
            opacity: ${Math.random() * 0.5 + 0.3};
            pointer-events: none;
            z-index: 1;
            animation: floatParticle ${Math.random() * 10 + 10}s linear infinite;
        `;
        
        document.body.appendChild(particle);
    }
}

// Add CSS for floating particles
const particleStyle = document.createElement('style');
particleStyle.textContent = `
    @keyframes floatParticle {
        0% {
            transform: translateY(0vh) rotate(0deg);
            opacity: 0;
        }
        10% {
            opacity: 1;
        }
        90% {
            opacity: 1;
        }
        100% {
            transform: translateY(-100vh) rotate(360deg);
            opacity: 0;
        }
    }
    
    .ripple {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.3);
        transform: scale(0);
        animation: rippleEffect 0.6s linear;
        pointer-events: none;
    }
    
    @keyframes rippleEffect {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
`;
document.head.appendChild(particleStyle);

// Add enhanced panel animations
function enhancePanels() {
    const panels = document.querySelectorAll('.panel, .card');
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.animation = 'floatIn 0.8s ease-out forwards';
            }
        });
    }, { threshold: 0.1 });
    
    panels.forEach(panel => {
        observer.observe(panel);
    });
}

// Add typing effect to title
function addTypingEffect() {
    const title = document.querySelector('.title');
    if (title) {
        const text = title.textContent;
        title.textContent = '';
        title.style.borderRight = '3px solid #ff1493';
        
        let i = 0;
        const typeWriter = () => {
            if (i < text.length) {
                title.textContent += text.charAt(i);
                i++;
                setTimeout(typeWriter, 100);
            } else {
                title.style.borderRight = 'none';
            }
        };
        
        setTimeout(typeWriter, 1000);
    }
}

// Add enhanced hover effects
function addEnhancedHoverEffects() {
    // Add glow effect to interactive elements
    const interactiveElements = document.querySelectorAll('.btn, .tone, .drop-zone, .panel');
    
    interactiveElements.forEach(element => {
        element.addEventListener('mouseenter', function() {
            this.style.filter = 'brightness(1.1) contrast(1.05)';
        });
        
        element.addEventListener('mouseleave', function() {
            this.style.filter = '';
        });
    });
}

// Add loading state to generate button
function addLoadingState() {
    const generateBtn = document.getElementById('generateBtn');
    if (generateBtn) {
        generateBtn.addEventListener('click', function() {
            this.classList.add('loading');
            this.innerHTML = '<span class="btn-icon" aria-hidden="true">⏳</span><span>Generating...</span>';
            
            // Remove loading state after 3 seconds (simulate generation)
            setTimeout(() => {
                this.classList.remove('loading');
                this.innerHTML = '<span class="btn-icon" aria-hidden="true"><svg width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polygon points="5 3 19 12 5 21 5 3" /></svg></span><span>Generate Audiobook</span>';
            }, 3000);
        });
    }
}

// Initialize all enhancements when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    createCursorTrail();
    enhanceButtons();
    createFloatingParticles();
    enhancePanels();
    addTypingEffect();
    addEnhancedHoverEffects();
    addLoadingState();
});
